<script setup lang="ts">
import Taro from '@tarojs/taro'

const avatarUrl = ref('https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0')
const nickname = ref('')

function onChooseAvatar(e: any) {
  const { avatarUrl: tempAvatarUrl } = e.detail
  avatarUrl.value = tempAvatarUrl

  Taro.showToast({
    title: '头像已更新',
    icon: 'success',
  })
}

function onNicknameChange(e: any) {
  nickname.value = e.detail.value
}

// 获取用户信息
async function getUserInfo() {
  try {
    const userInfo = Taro.getStorageSync('userStore')
    if (userInfo && userInfo.nickName) {
      nickname.value = userInfo.nickName
    }
  }
  catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

onMounted(() => {
  getUserInfo()
})
</script>

<template>
  <div class="profile-container">
    <div class="profile-header">
      <text class="title">
个人信息设置
</text>
    </div>

    <div class="profile-content">
      <!-- 头像选择 -->
      <div class="avatar-section">
        <text class="label">
头像
</text>
        <button class="avatar-btn" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
          <image class="avatar" :src="avatarUrl" mode="aspectFill" />
          <text class="avatar-tip">
点击更换头像
</text>
        </button>
      </div>

      <!-- 昵称显示 -->
      <div class="nickname-section">
        <text class="label">
          昵称
        </text>
        <input
          class="nickname-input"
          type="nickname"
          :value="nickname"
          placeholder="请输入昵称"
          @blur="onNicknameChange"
        >
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.profile-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px;
}

.profile-header {
  text-align: center;
  margin-bottom: 40px;

  .title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
  }
}

.profile-content {
  background-color: #fff;
  border-radius: 12px;
  padding: 24px;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 32px;

  .label {
    font-size: 16px;
    color: #333;
    margin-bottom: 16px;
  }

  .avatar-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: none;
    border: none;
    padding: 0;

    .avatar {
      width: 80px;
      height: 80px;
      border-radius: 40px;
      border: 2px solid #e5e5e5;
    }

    .avatar-tip {
      font-size: 12px;
      color: #999;
      margin-top: 8px;
    }
  }
}

.nickname-section {
  .label {
    font-size: 16px;
    color: #333;
    margin-bottom: 12px;
    display: block;
  }

  .nickname-input {
    width: 100%;
    height: 44px;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    padding: 0 12px;
    font-size: 16px;
    background-color: #fafafa;
  }
}
</style>
